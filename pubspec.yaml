name: connectone
description: Connect One Customer App

publish_to: 'none'

version: 1.5.5+8

environment:
  sdk: ">=2.16.1 <3.0.0"

dependencies:
  flutter:
    sdk: flutter
  amazon_cognito_identity_dart_2: ^3.6.5
  audioplayers: ^5.1.0
  auto_size_text: ^3.0.0
  awesome_dialog: ^3.0.2
  awesome_notifications: ^0.7.7
  badges: ^3.1.2
  change_app_package_name: ^1.1.0
  community_charts_flutter: ^1.0.2
  cloud_firestore: ^4.9.2
  connectivity_plus: ^5.0.0
  cupertino_icons: ^1.0.2
  dartz: ^0.10.1
  device_info_plus: ^9.0.3
  dio: ^5.3.3
  dio_smart_retry: ^5.0.0
  easy_image_viewer: ^1.2.0
  equatable: ^2.0.3
  fbroadcast: ^2.0.0
  firebase_analytics: ^10.5.0
  firebase_auth: ^4.17.5
  firebase_core: ^2.25.4
  firebase_core_platform_interface: ^5.0.0
  firebase_crashlytics: ^3.3.6
  firebase_database: ^10.2.6
  firebase_messaging: ^14.6.8
  firebase_remote_config: ^4.2.6
  flash: ^3.0.5+1
  dropdown_search: ^5.0.6
  # flutter_barcode_scanner: ^2.0.0
  flutter_bloc: ^8.0.1
  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.2.19
  flutter_rating_bar: ^4.0.1
  flutter_smart_dialog: ^4.9.0+4
  fluttertoast: ^8.2.6
  get: ^4.6.1
  get_storage: ^2.0.3
  google_fonts: ^6.1.0
  http: ^1.3.0
  icons_launcher: ^2.1.0
  intl: ^0.18.1
  lazy_load_scrollview: ^1.3.0
  location: ^5.0.3
  map_launcher: ^2.4.0
  mapbox_maps_flutter: ^0.4.0
  multi_select_flutter: ^4.1.2
  #  package_info_plus: ^4.1.0
  # platform_device_id: ^1.0.1
  readmore: ^2.2.0
  rive: ^0.11.17
  rotated_corner_decoration: 2.1.0+1
  rounded_loading_button: ^2.1.0
  shared_preferences: ^2.0.17
  syncfusion_flutter_core: ^21.2.10
  syncfusion_flutter_sliders: ^21.2.4
  table_calendar: ^3.0.6
  timezone: ^0.9.1
  turf: ^0.0.7
  url_launcher: ^6.2.4
  vibration: ^1.8.1
  vibration_web: ^1.6.5
  webview_flutter: ^3.0.2
  loader_overlay: ^2.3.0
  modal_progress_hud_nsn: ^0.4.0
  flutter_screen_lock: ^9.0.4
  local_auth: ^2.1.8
  flutter_typeahead: ^5.2.0
  file_picker: ^8.0.5
  flutter_sound: ^9.2.13
  audio_session: ^0.1.19
  permission_handler: ^11.2.0
  share_plus: ^7.0.0
  razorpay_flutter: ^1.3.7
  cached_network_image: ^3.3.1
  image_picker: ^1.1.2
  path: ^1.9.0
  path_provider: ^2.1.4
  pdf: ^3.11.1
  printing: ^5.13.1
  app_links: ^6.3.3
  mime: ^1.0.6
  tutorial_coach_mark: 1.2.13
  # social_media_recorder: ^1.1.13

  # just_audio: ^0.9.34

dev_dependencies:
  flutter_lints: ^2.0.3
  flutter_test:
    sdk: flutter

flutter_native_splash:
  color: '#ffffff'
  image: assets/images/bai.png
  color_dark: '#ffffff'
  icon_background_color_dark: '#ffffff'
  android_12:
    color: '#ffffff'
    icon_background_color: '#ffffff'
  fullscreen: true

icons_launcher:
  image_path: "assets/images/bai.png"
  platforms:
    android:
      enable: true
    ios:
      enable: true

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/sounds/
  fonts:
    - family: avenir_light
      fonts:
        - asset: assets/fonts/avenir_light.ttf
    - family: avenir_black
      fonts:
        - asset: assets/fonts/avenir_black.otf
    - family: avenir_medium
      fonts:
        - asset: assets/fonts/avenir_medium.ttf
    - family: helvetica_neu
      fonts:
        - asset: assets/fonts/helvetica_neu.otf
    - family: poppins
      fonts:
        - asset: assets/fonts/Archivo-Light.ttf
    - family: archivo
      fonts:
        - asset: assets/fonts/Archivo-Regular.ttf
